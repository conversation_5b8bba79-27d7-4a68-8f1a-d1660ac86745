package com.yingfei.dataManagement.service.report.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.DelFlagEnum;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.dataManagement.mapper.STREAM_TREND_INFMapper;
import com.yingfei.dataManagement.service.EMPL_RESPONSIBLE_INFService;
import com.yingfei.dataManagement.service.SPEC_INFService;
import com.yingfei.dataManagement.service.report.MonthTrendService;
import com.yingfei.entity.domain.*;
import com.yingfei.entity.dto.EMPL_INF_DTO;
import com.yingfei.entity.dto.SPEC_INF_DTO;
import com.yingfei.entity.dto.STREAM_TREND_INF_DTO;
import com.yingfei.entity.dto.SubgroupDataDTO;
import com.yingfei.entity.dto.report.MonthTrendQueryDTO;
import com.yingfei.entity.dto.report.MonthTrendResultDTO;
import com.yingfei.entity.vo.EMPL_INF_VO;
import com.yingfei.system.api.RemoteUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 月度能力趋势报表服务实现类
 *
 * 主要功能：
 * 1. 查询月度能力趋势数据（f_type = 1）
 * 2. 批量查询项目负责人信息
 * 3. 批量查询公差限信息
 * 4. 计算改善幅度和能力分析
 *
 * 性能优化：
 * - 分页后批量查询，避免N+1问题
 * - 使用Map缓存查询结果
 * - 合理的异常处理和日志记录
 *
 * <AUTHOR>
 * @version 2.0
 */
@Slf4j
@Service
public class MonthTrendServiceImpl implements MonthTrendService {

    // ==================== 常量定义 ====================

    /** 月度统计类型 */
    private static final int MONTHLY_TYPE = 1;

    /** 员工与测试映射类型 */
    private static final int EMPLOYEE_TEST_MAPPING_TYPE = 0;

    /** 默认查询月份数 */
    private static final int DEFAULT_MONTHS = 4;

    /** 日期格式化器 */
    private static final DateTimeFormatter MONTH_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM");

    // ==================== 内部类定义 ====================




    /**
     * 公差限信息
     */
    private static class SpecInfo {
        private final Double usl;
        private final Double tar;
        private final Double lsl;
        private final Double targetCpk;

        public SpecInfo(Double usl, Double tar, Double lsl, Double targetCpk) {
            this.usl = usl;
            this.tar = tar;
            this.lsl = lsl;
            this.targetCpk = targetCpk;
        }

        public Double getUsl() { return usl; }
        public Double getTar() { return tar; }
        public Double getLsl() { return lsl; }
        public Double getTargetCpk() { return targetCpk; }
    }



    // ==================== 依赖注入 ====================

    @Resource
    private STREAM_TREND_INFMapper streamTrendInfMapper;

    @Resource
    private SPEC_INFService specInfService;

    @Resource
    private EMPL_RESPONSIBLE_INFService emplResponsibleInfService;

    @Resource
    private RemoteUserService remoteUserService;


    @Override
    public TableDataInfo<MonthTrendResultDTO> getMonthTrend(MonthTrendQueryDTO monthTrendQueryDTO) {
        // 1. 参数处理和默认值设置
        processQueryParameters(monthTrendQueryDTO);

        // 查询STREAM_TREND_INF数据（f_type = 1 按月统计）
        List<STREAM_TREND_INF_DTO> streamTrendData = queryStreamTrendData(monthTrendQueryDTO);

        // 按产品、过程、测试、版本分组处理数据
        Map<String, List<STREAM_TREND_INF_DTO>> groupedData = streamTrendData.stream()
                .collect(Collectors.groupingBy(item ->
                    item.getF_PART() + "_" + item.getF_PRCS() + "_" + item.getF_TEST() + "_" + item.getF_PTRV()));

        // 构建返回结果（不包含负责人信息）
        List<MonthTrendResultDTO> resultList = new ArrayList<>();
        for (Map.Entry<String, List<STREAM_TREND_INF_DTO>> entry : groupedData.entrySet()) {
            MonthTrendResultDTO resultDTO = buildMonthTrendResult(entry.getValue(), monthTrendQueryDTO);
            if (resultDTO != null) {
                resultList.add(resultDTO);
            }
        }

        // 分页处理
        final Page<MonthTrendResultDTO> page = BaseEntity.convertToPage(monthTrendQueryDTO.getOffset(), monthTrendQueryDTO.getNext());
        int start = (int) ((page.getCurrent() - 1) * page.getSize());
        int end = Math.min(start + (int) page.getSize(), resultList.size());

        List<MonthTrendResultDTO> pagedResult = resultList.subList(start, end);
        page.setRecords(pagedResult);
        page.setTotal(resultList.size());

        // 批量查询当前页面的项目负责人和公差限信息
        if (!pagedResult.isEmpty()) {
            // 提取测试相关的唯一标识
            Set<Long> testIds = pagedResult.stream()
                    .map(MonthTrendResultDTO::getTestId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            // 批量查询负责人信息
            Map<String, List<MonthTrendResultDTO.ResponsiblePersonInfo>> responsiblePersonMap = batchQueryResponsiblePersons(testIds);

            // 批量查询公差限信息
            Map<String, SpecInfo> specInfoMap = batchQuerySpecInfo(pagedResult);

            // 为当前页面的数据设置负责人和公差限信息
            pagedResult.forEach(result -> {
                // 设置负责人信息
                List<MonthTrendResultDTO.ResponsiblePersonInfo> responsiblePerson = responsiblePersonMap.get(result.getTestId()+"");
                if (CollectionUtils.isNotEmpty(responsiblePerson)) {
                    result.setResponsiblePerson(responsiblePerson);
                }
                SubgroupDataDTO subgroupDataDTO1 = new SubgroupDataDTO();
                subgroupDataDTO1.setF_PART(result.getPartId());
                subgroupDataDTO1.setF_PRCS(result.getPrcsId());
                subgroupDataDTO1.setF_TEST( result.getTestId());
                subgroupDataDTO1.setF_REV(result.getPtrvId());
                SPEC_INF_DTO specInfo = specInfService.getSpecLim(subgroupDataDTO1);



                // 设置公差限信息
//                String specKey = result.getPartId() + "_" + result.getPrcsId() + "_" + result.getTestId() + "_" + result.getPtrvId();
//                SpecInfo specInfo = specInfoMap.get(specKey);
                if (specInfo != null) {
                    result.setUsl(specInfo.getF_USL());
                    result.setTar(specInfo.getF_TAR());
                    result.setLsl(specInfo.getF_LSL());
                    result.setTargetCpk(specInfo.getF_CPK());
                }
            });
        }
        return new TableDataInfo<>(pagedResult,page.getTotal());
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 处理查询参数，设置默认值
     */
    private void processQueryParameters(MonthTrendQueryDTO queryDTO) {
        if (ObjectUtils.isEmpty(queryDTO.getStartDate())) {
            LocalDate today = LocalDate.now();
            Date startDate = Date.from(today.minusMonths(DEFAULT_MONTHS)
                    .withDayOfMonth(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
            Date endDate = Date.from(
                    today.minusMonths(1)
                            .withDayOfMonth(1)
                            .atStartOfDay(ZoneId.systemDefault())
                            .toInstant());
            queryDTO.setStartDate(startDate);
            queryDTO.setEndDate(endDate);
        }

        log.info("查询参数：开始时间={}，结束时间={}，产品数量={}，过程数量={}，测试数量={}，版本数量={}",
                queryDTO.getStartDate(), queryDTO.getEndDate(),
                CollectionUtils.size(queryDTO.getPartIdList()),
                CollectionUtils.size(queryDTO.getPrcsIdList()),
                CollectionUtils.size(queryDTO.getTestIdList()),
                CollectionUtils.size(queryDTO.getPtrvIdList()));
    }

    /**
     * 查询STREAM_TREND_INF数据
     * @param queryDTO 查询条件
     * @return 查询结果列表
     */
    private List<STREAM_TREND_INF_DTO> queryStreamTrendData(MonthTrendQueryDTO queryDTO) {

        MPJLambdaWrapper<STREAM_TREND_INF> wrapper = new MPJLambdaWrapper<>();
        wrapper.leftJoin(PART_INF.class, PART_INF::getF_PART, STREAM_TREND_INF::getF_PART);
        wrapper.leftJoin(PART_REV.class, PART_REV::getF_PTRV, STREAM_TREND_INF::getF_PTRV);
        wrapper.leftJoin(PRCS_INF.class, PRCS_INF::getF_PRCS, STREAM_TREND_INF::getF_PRCS);
        wrapper.leftJoin(TEST_INF.class, TEST_INF::getF_TEST, STREAM_TREND_INF::getF_TEST);

        wrapper.selectAll(STREAM_TREND_INF.class)
                .selectAs(PART_INF::getF_NAME, STREAM_TREND_INF_DTO::getPartName)
                .selectAs(PART_REV::getF_NAME, STREAM_TREND_INF_DTO::getPtrvName)
                .selectAs(PRCS_INF::getF_NAME, STREAM_TREND_INF_DTO::getPrcsName)
                .selectAs(TEST_INF::getF_NAME, STREAM_TREND_INF_DTO::getTestName);

        // 添加查询条件
        if(ObjectUtils.isNotEmpty(queryDTO.getPartIdList())){
            wrapper.in(STREAM_TREND_INF::getF_PART, queryDTO.getPartIdList());
        }
        if(ObjectUtils.isNotEmpty(queryDTO.getPtrvIdList())){
            wrapper.in(STREAM_TREND_INF::getF_PTRV, queryDTO.getPtrvIdList());
        }
        if(ObjectUtils.isNotEmpty(queryDTO.getPrcsIdList())){
            wrapper.in(STREAM_TREND_INF::getF_PRCS, queryDTO.getPrcsIdList());
        }
        if(ObjectUtils.isNotEmpty(queryDTO.getTestIdList())){
            wrapper.in(STREAM_TREND_INF::getF_TEST, queryDTO.getTestIdList());
        }

        // 关键条件：f_type = 1（按月统计）
        wrapper.eq(STREAM_TREND_INF::getF_DEL, YesOrNoEnum.NO.getType());
        wrapper.eq(STREAM_TREND_INF::getF_TYPE, MONTHLY_TYPE);
        wrapper.ge(STREAM_TREND_INF::getF_START, queryDTO.getStartDate());
        wrapper.le(STREAM_TREND_INF::getF_START, queryDTO.getEndDate());
        wrapper.orderByAsc(STREAM_TREND_INF::getF_START);

        return streamTrendInfMapper.selectJoinList(STREAM_TREND_INF_DTO.class, wrapper);
    }

    /**
     * 构建月度趋势结果
     */
    private MonthTrendResultDTO buildMonthTrendResult(List<STREAM_TREND_INF_DTO> dataList, MonthTrendQueryDTO queryDTO) {
        if (dataList == null || dataList.isEmpty()) {
            return null;
        }

        STREAM_TREND_INF_DTO firstItem = dataList.get(0);
        MonthTrendResultDTO result = new MonthTrendResultDTO();

        // 设置基本信息
        result.setPartId(firstItem.getF_PART());
        result.setPartName(firstItem.getPartName());
        result.setPtrvId(firstItem.getF_PTRV());
        result.setPtrvName(firstItem.getPtrvName());
        result.setPrcsId(firstItem.getF_PRCS());
        result.setPrcsName(firstItem.getPrcsName());
        result.setTestId(firstItem.getF_TEST());
        result.setTestName(firstItem.getTestName());

        // 公差限信息将在分页后批量查询并设置

        // 构建完整的月度数据（包含数据补全和lastMonth处理）
        buildCompleteMonthlyData(result, dataList, queryDTO);

        // 计算改善幅度和能力分析
        result.calculateImprovementRate();
        result.analyzeCapability();

        return result;
    }

    /**
     * 构建完整的月度数据，包含数据补全和lastMonth处理
     */
    private void buildCompleteMonthlyData(MonthTrendResultDTO result, List<STREAM_TREND_INF_DTO> dataList, MonthTrendQueryDTO queryDTO) {
        // 1. 生成查询时间范围内的所有月份列表
        List<String> allMonths = generateMonthList(queryDTO.getStartDate(), queryDTO.getEndDate());

        // 2. 从数据中提取实际有数据的月份
        Map<String, Integer> actualSubgroupCount = new HashMap<>();
        Map<String, Double> actualCpk = new HashMap<>();

        for (STREAM_TREND_INF_DTO item : dataList) {
            if (item.getF_START() != null) {
                String monthKey = item.getF_START().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(MONTH_FORMATTER);
                actualSubgroupCount.put(monthKey, item.getF_SGRP_COUNT());
                actualCpk.put(monthKey, item.getF_CPK());
            }
        }

        // 3. 获取最后一个月份（用于lastMonth数据）
        String lastMonth = allMonths.isEmpty() ? null : allMonths.get(allMonths.size() - 1);

        // 4. 构建补全的月度数据（剔除最后一个月）- 使用LinkedHashMap保持日期正序
        Map<String, Integer> monthlySubgroupCount = new LinkedHashMap<>();
        Map<String, Double> monthlyCpk = new LinkedHashMap<>();

        // 按日期正序遍历所有月份
        for (String month : allMonths) {
            // 跳过最后一个月，lastMonth数据单独处理
            if (!month.equals(lastMonth)) {
                monthlySubgroupCount.put(month, actualSubgroupCount.get(month)); // 没有数据时为null
                monthlyCpk.put(month, actualCpk.get(month)); // 没有数据时为null
            }
        }

        result.setMonthlySubgroupCount(monthlySubgroupCount);
        result.setMonthlyCpk(monthlyCpk);

        // 5. 处理lastMonth相关数据
        calculateLatestAndPreviousData(result, monthlyCpk, lastMonth, actualCpk,actualSubgroupCount);
    }

    /**
     * 生成指定时间范围内的所有月份列表
     */
    private List<String> generateMonthList(Date startDate, Date endDate) {
        List<String> months = new ArrayList<>();

        LocalDate start = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().withDayOfMonth(1);
        LocalDate end = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().withDayOfMonth(1);

        LocalDate current = start;
        while (!current.isAfter(end)) {
            months.add(current.format(MONTH_FORMATTER));
            current = current.plusMonths(1);
        }

        return months;
    }



    /**
     * 批量查询公差限信息 - 使用IN查询优化
     */
    private Map<String, SpecInfo> batchQuerySpecInfo(List<MonthTrendResultDTO> resultList) {
        if (CollectionUtils.isEmpty(resultList)) {
            return new HashMap<>();
        }

        try {
            // 提取所有唯一的查询条件
            Set<Long> partIds = new HashSet<>();
            Set<Long> prcsIds = new HashSet<>();
            Set<Long> testIds = new HashSet<>();
            Set<Long> ptrvIds = new HashSet<>();

            for (MonthTrendResultDTO result : resultList) {
                if (result.getPartId() != null) partIds.add(result.getPartId());
                if (result.getPrcsId() != null) prcsIds.add(result.getPrcsId());
                if (result.getTestId() != null) testIds.add(result.getTestId());
                if (result.getPtrvId() != null) ptrvIds.add(result.getPtrvId());
            }

            // 使用IN查询批量获取公差限信息
            LambdaQueryWrapper<SPEC_INF> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(SPEC_INF::getF_PART, partIds)
                   .in(SPEC_INF::getF_PRCS, prcsIds)
                   .in(SPEC_INF::getF_TEST, testIds)
                   .in(SPEC_INF::getF_PTRV, ptrvIds)
                   .eq(SPEC_INF::getF_DEL, YesOrNoEnum.NO.getType());

            List<SPEC_INF> specInfList = specInfService.list(wrapper);

            if (CollectionUtils.isEmpty(specInfList)) {
                log.info("未查询到公差限信息，查询条件：partIds={}, prcsIds={}, testIds={}, ptrvIds={}",
                        partIds, prcsIds, testIds, ptrvIds);
                return new HashMap<>();
            }

            // 构建结果Map：key为"partId_prcsId_testId_ptrvId"，value为SpecInfo
            Map<String, SpecInfo> specInfoMap = new HashMap<>();
            for (SPEC_INF specInf : specInfList) {
                String key = specInf.getF_PART() + "_" + specInf.getF_PRCS() + "_" +
                           specInf.getF_TEST() + "_" + specInf.getF_PTRV();

                SpecInfo specInfo = new SpecInfo(
                        specInf.getF_USL(),
                        specInf.getF_TAR(),
                        specInf.getF_LSL(),
                        specInf.getF_CPK()
                );

                specInfoMap.put(key, specInfo);
            }

            log.info("批量查询公差限信息完成，查询到{}条记录", specInfoMap.size());
            return specInfoMap;

        } catch (Exception e) {
            log.error("批量查询公差限信息失败，resultList size: {}, error: {}",
                     resultList.size(), e.getMessage(), e);
            return new HashMap<>();
        }
    }

    /**
     * 批量查询项目负责人信息
     */
    private Map<String, List<MonthTrendResultDTO.ResponsiblePersonInfo>> batchQueryResponsiblePersons(Set<Long> testIds) {
        if (testIds.isEmpty()) {
            return new HashMap<>();
        }

        // 批量查询EMPL_RESPONSIBLE_INF表
        List<String> testIdStrings = testIds.stream()
                .map(String::valueOf)
                .collect(Collectors.toList());

        LambdaQueryWrapper<EMPL_RESPONSIBLE_INF> responsibleWrapper = new LambdaQueryWrapper<>();
        responsibleWrapper.in(EMPL_RESPONSIBLE_INF::getF_DATA, testIdStrings)
                .eq(EMPL_RESPONSIBLE_INF::getF_TYPE, EMPLOYEE_TEST_MAPPING_TYPE)
                .eq(EMPL_RESPONSIBLE_INF::getF_DEL, DelFlagEnum.USE.getType());

        // 确认testIds不为空
        if (!CollectionUtils.isEmpty(testIds)) {
            responsibleWrapper.eq(EMPL_RESPONSIBLE_INF::getF_TYPE, 0);
            // 直接对F_DATA字段进行IN查询，无需JSON解析
            final Object[] testIdArray = testIdStrings.toArray();
            // 一个测试只能有一个负责人，使用OR关系匹配数组中的任一测试ID
            if (testIdArray.length > 0) {
                // 开启第一个条件分组
                responsibleWrapper.and(wrapper -> {
                    for (int i = 0; i < testIdArray.length; i++) {
                        String testId = testIdArray[i].toString();
                        if (i == 0) {
                            // 第一个条件直接添加
                            wrapper.like(EMPL_RESPONSIBLE_INF::getF_DATA, testId);
                        } else {
                            // 后续条件使用OR连接
                            wrapper.or().like(EMPL_RESPONSIBLE_INF::getF_DATA, testId);
                        }
                    }
                });
            }
        }


        List<EMPL_RESPONSIBLE_INF> responsibleList = emplResponsibleInfService.list(responsibleWrapper);

        if (responsibleList.isEmpty()) {
            return new HashMap<>();
        }

        Map<String, List<Long>> testIdToEmpls = getTestIdToEmpls(responsibleList);
        Map<String, List<MonthTrendResultDTO.ResponsiblePersonInfo>> result = new HashMap<>();

        // 提取员工ID列表
        Set<Long> emplIds = responsibleList.stream()
                .map(EMPL_RESPONSIBLE_INF::getF_EMPL)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (emplIds.isEmpty()) {
            return new HashMap<>();
        }

        // 批量查询员工信息
        Map<Long, EMPL_INF_DTO> userMap = new HashMap<>();
        try {
            List<Long> longList = new ArrayList<>(emplIds);
            final EMPL_INF_VO emplInfVo = new EMPL_INF_VO();
            emplInfVo.setIds(longList);
            final R<List<EMPL_INF_DTO>> list = remoteUserService.getList(emplInfVo);
            System.out.println("远程查询员工信息结果" + list.toString());
            List<EMPL_INF_DTO> adminUserList = list.getData();
            userMap = adminUserList.stream()
                    // 以id为键，对象本身为值
                    .collect(Collectors.toMap(EMPL_INF_DTO::getF_EMPL, user -> user));
        } catch (Exception e) {
            e.printStackTrace();
            log.warn("远程查询员工信息失败，emplId: {}, error: {}", emplIds, e.getMessage());
        }

        for (String testId : testIdToEmpls.keySet()) {
            List<Long> emplIdList = testIdToEmpls.get(testId);
            List<MonthTrendResultDTO.ResponsiblePersonInfo> responsiblePersonInfos = emplIdList.stream()
                    .map(userMap::get)
                    .filter(Objects::nonNull)
                    .map(emplInfDto -> new MonthTrendResultDTO.ResponsiblePersonInfo(emplInfDto.getF_EMPL(), emplInfDto.getF_NAME()))
                    .collect(Collectors.toList());
            result.put(testId, responsiblePersonInfos);
        }
        return result;
    }


    /**
     * 获取每个测试ID对应的员工集合
     */
    public static Map<String, List<Long>> getTestIdToEmpls(List<EMPL_RESPONSIBLE_INF> responsibleList) {
        // 存储测试ID与员工集合的映射关系
        Map<String, List<Long>> resultMap = new HashMap<>();

        // 遍历所有责任信息
        for (EMPL_RESPONSIBLE_INF info : responsibleList) {
            // 拆分测试ID字符串（假设使用逗号分隔）
            String[] testIds = info.getF_DATA().split(",");
            Long empl = info.getF_EMPL();

            // 为每个测试ID添加对应的员工
            for (String testId : testIds) {
                // 去除可能的空格
                testId = testId.trim();

                // 如果Map中没有该测试ID，先创建一个空列表
                if (!resultMap.containsKey(testId)) {
                    resultMap.put(testId, new ArrayList<>());
                }

                // 将员工添加到对应的列表中
                final List<Long> longs = resultMap.get(testId);
                longs.add(empl);
                resultMap.put(testId, longs);
            }
        }
            return resultMap;
    }






    /**
     * 计算最新月份和上月数据
     * @param result 结果对象
     * @param monthlyCpk 月度CPK数据（已剔除lastMonth）
     * @param lastMonth 最后一个月份
     * @param actualCpk 实际CPK数据（包含lastMonth）
     *                  用于获取最新月份的CPK值
     * @param actualSubgroupCount 实际子组数量数据（包含lastMonth）
     *                  用于获取最新月份的子组数量
     */
    private void calculateLatestAndPreviousData(MonthTrendResultDTO result, Map<String, Double> monthlyCpk,
                                              String lastMonth, Map<String, Double> actualCpk,Map<String, Integer> actualSubgroupCount) {
        // 1. 设置lastMonth相关数据（显示最后一个月数据）
        if (lastMonth != null) {
            result.setLatestMonth(lastMonth);
            // 如果最后一个月没有数据，赋值null
            result.setLatestCpk(actualCpk.get(lastMonth));
            result.setLatestSubgroupCount(actualSubgroupCount.get(lastMonth));
        }

        // 2. 计算上月数据（从剔除lastMonth的数据中获取最新的）
        if (!monthlyCpk.isEmpty()) {
            // 按月份排序，获取最新的月份作为上月数据
            List<String> sortedMonths = monthlyCpk.keySet().stream()
                    .filter(month -> monthlyCpk.get(month) != null) // 只考虑有数据的月份
                    .sorted(Comparator.reverseOrder())
                    .collect(Collectors.toList());

            if (!sortedMonths.isEmpty()) {
                String previousMonth = sortedMonths.get(0);
                result.setPreviousCpk(monthlyCpk.get(previousMonth));
            }
        }
    }


}
